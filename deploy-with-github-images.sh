#!/bin/bash

# ROBO-RESEARCHER-2000 Deployment Script with GitHub Images Fallback
# This script provides an alternative deployment method using GitHub Container Registry

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
GITHUB_REGISTRY="ghcr.io/robo-researcher-2000"
DOCKER_HUB_REGISTRY="docker.io"
PROJECT_NAME="robo-researcher-2000"
COMPOSE_FILE_OFFICIAL="docker-compose.yml"
COMPOSE_FILE_GITHUB="docker-compose.github.yml"

# Required images
declare -A IMAGES=(
    ["n8n"]="n8nio/n8n:latest"
    ["postgres"]="postgres:15"
    ["minio"]="minio/minio:latest"
    ["wikijs"]="requarks/wiki:2"
    ["redis"]="redis:7-alpine"
    ["nginx"]="nginx:alpine"
)

# GitHub alternative images
declare -A GITHUB_IMAGES=(
    ["n8n"]="$GITHUB_REGISTRY/n8n:latest"
    ["postgres"]="$GITHUB_REGISTRY/postgres:15"
    ["minio"]="$GITHUB_REGISTRY/minio:latest"
    ["wikijs"]="$GITHUB_REGISTRY/wikijs:2"
    ["redis"]="$GITHUB_REGISTRY/redis:7-alpine"
    ["nginx"]="$GITHUB_REGISTRY/nginx:alpine"
)

# Functions
print_header() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                ROBO-RESEARCHER-2000 DEPLOYMENT               ║"
    echo "║              GitHub Images Fallback Installer               ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_step() {
    echo -e "${GREEN}[STEP]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    print_step "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    print_info "✓ Docker found: $(docker --version)"
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    print_info "✓ Docker Compose found"
    
    # Check Docker daemon
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running. Please start Docker first."
        exit 1
    fi
    print_info "✓ Docker daemon is running"
    
    # Check available disk space (minimum 10GB)
    available_space=$(df . | awk 'NR==2 {print $4}')
    if [ "$available_space" -lt 10485760 ]; then  # 10GB in KB
        print_warning "Less than 10GB disk space available. Deployment may fail."
    else
        print_info "✓ Sufficient disk space available"
    fi
}

test_image_availability() {
    local image=$1
    print_info "Testing availability of $image..."
    
    if docker pull "$image" &> /dev/null; then
        print_info "✓ $image is available"
        return 0
    else
        print_warning "✗ $image is not available"
        return 1
    fi
}

test_official_images() {
    print_step "Testing official Docker Hub images..."
    
    local failed_images=()
    local total_images=${#IMAGES[@]}
    local available_images=0
    
    for service in "${!IMAGES[@]}"; do
        image="${IMAGES[$service]}"
        if test_image_availability "$image"; then
            ((available_images++))
        else
            failed_images+=("$service:$image")
        fi
    done
    
    echo
    print_info "Image availability: $available_images/$total_images"
    
    if [ ${#failed_images[@]} -eq 0 ]; then
        print_info "✓ All official images are available"
        return 0
    else
        print_warning "✗ Some official images are not available:"
        for failed in "${failed_images[@]}"; do
            print_warning "  - $failed"
        done
        return 1
    fi
}

prompt_github_fallback() {
    echo
    print_warning "Some official Docker images are not available."
    print_info "We can use alternative images from GitHub Container Registry instead."
    echo
    echo -e "${YELLOW}GitHub Container Registry Images:${NC}"
    for service in "${!GITHUB_IMAGES[@]}"; do
        echo "  - $service: ${GITHUB_IMAGES[$service]}"
    done
    echo
    
    while true; do
        read -p "Do you want to use GitHub Container Registry images? (y/n): " yn
        case $yn in
            [Yy]* ) return 0;;
            [Nn]* ) return 1;;
            * ) echo "Please answer yes or no.";;
        esac
    done
}

setup_environment() {
    print_step "Setting up environment..."
    
    # Create directories
    mkdir -p volumes/{n8n_data,postgres_data,minio_data,wikijs_data,redis_data,nginx_logs}
    mkdir -p {workflows,custom-nodes,init-scripts,wiki-config,client-config,nginx}
    
    print_info "✓ Created directory structure"
    
    # Create .env file if it doesn't exist
    if [ ! -f .env ]; then
        print_info "Creating .env file..."
        cat > .env << EOF
# ROBO-RESEARCHER-2000 Environment Configuration
# Generated on $(date)

# Database Configuration
POSTGRES_DB=n8n
POSTGRES_USER=n8n_user
POSTGRES_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)

# n8n Configuration
N8N_HOST=localhost
N8N_PROTOCOL=http
N8N_SECURE_COOKIE=false
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=robo-researcher-2000
N8N_LOG_LEVEL=info
GENERIC_TIMEZONE=UTC

# MinIO Configuration
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)

# Redis Configuration
REDIS_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)

# Wiki.js Configuration
WIKI_ADMIN_EMAIL=<EMAIL>

# API Keys (Add your keys here)
OPENROUTER_API_KEY=your_openrouter_api_key_here
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password_here
EOF
        print_info "✓ Created .env file with secure passwords"
    else
        print_info "✓ .env file already exists"
    fi
    
    # Set proper permissions
    chmod 600 .env
    chmod -R 755 volumes/
    
    print_info "✓ Set proper permissions"
}

pull_github_images() {
    print_step "Pulling GitHub Container Registry images..."
    
    for service in "${!GITHUB_IMAGES[@]}"; do
        image="${GITHUB_IMAGES[$service]}"
        print_info "Pulling $image..."
        
        if docker pull "$image"; then
            print_info "✓ Successfully pulled $image"
        else
            print_error "✗ Failed to pull $image"
            print_info "Attempting to build image locally..."
            build_local_image "$service"
        fi
    done
}

build_local_image() {
    local service=$1
    local original_image="${IMAGES[$service]}"
    local github_image="${GITHUB_IMAGES[$service]}"
    
    print_info "Building local image for $service..."
    
    # Create temporary Dockerfile
    cat > "Dockerfile.$service" << EOF
FROM $original_image
LABEL org.opencontainers.image.source=https://github.com/robo-researcher-2000/robo-researcher-2000
LABEL org.opencontainers.image.description="ROBO-RESEARCHER-2000 $service image"
EOF
    
    if docker build -t "$github_image" -f "Dockerfile.$service" .; then
        print_info "✓ Successfully built local image for $service"
        rm "Dockerfile.$service"
    else
        print_error "✗ Failed to build local image for $service"
        rm "Dockerfile.$service"
        return 1
    fi
}

deploy_services() {
    local compose_file=$1
    print_step "Deploying services using $compose_file..."
    
    # Start services
    if docker-compose -f "$compose_file" up -d; then
        print_info "✓ Services started successfully"
    else
        print_error "✗ Failed to start services"
        return 1
    fi
    
    # Wait for services to be healthy
    print_info "Waiting for services to be healthy..."
    sleep 30
    
    # Check service health
    check_service_health "$compose_file"
}

check_service_health() {
    local compose_file=$1
    print_step "Checking service health..."
    
    local services=("postgres" "redis" "minio" "n8n" "wikijs" "client")
    local healthy_services=0
    
    for service in "${services[@]}"; do
        if docker-compose -f "$compose_file" ps "$service" | grep -q "healthy\|Up"; then
            print_info "✓ $service is healthy"
            ((healthy_services++))
        else
            print_warning "✗ $service is not healthy"
        fi
    done
    
    echo
    print_info "Healthy services: $healthy_services/${#services[@]}"
    
    if [ "$healthy_services" -eq "${#services[@]}" ]; then
        print_info "✓ All services are healthy"
        return 0
    else
        print_warning "Some services are not healthy. Check logs with:"
        print_info "docker-compose -f $compose_file logs [service-name]"
        return 1
    fi
}

display_access_info() {
    print_step "Deployment completed!"
    echo
    echo -e "${GREEN}🎉 ROBO-RESEARCHER-2000 is now running!${NC}"
    echo
    echo -e "${BLUE}Access URLs:${NC}"
    echo "  🌐 Client Interface:    http://localhost:8080"
    echo "  ⚙️  n8n Workflows:      http://localhost:5678"
    echo "  📚 Wiki.js Docs:       http://localhost:3002"
    echo "  🗄️  MinIO Console:      http://localhost:9001"
    echo
    echo -e "${BLUE}Default Credentials:${NC}"
    echo "  n8n:     admin / robo-researcher-2000"
    echo "  MinIO:   minioadmin / [check .env file]"
    echo "  Wiki.js: Setup required on first access"
    echo
    echo -e "${YELLOW}Next Steps:${NC}"
    echo "  1. Configure your API keys in the client settings"
    echo "  2. Import the n8n workflows from the workflows/ directory"
    echo "  3. Set up Wiki.js with your preferred configuration"
    echo "  4. Test the complete workflow with a sample transcription"
    echo
    echo -e "${BLUE}Useful Commands:${NC}"
    echo "  View logs:    docker-compose logs -f [service-name]"
    echo "  Stop all:     docker-compose down"
    echo "  Restart:      docker-compose restart [service-name]"
    echo "  Update:       docker-compose pull && docker-compose up -d"
}

cleanup_on_error() {
    print_error "Deployment failed. Cleaning up..."
    docker-compose -f "$COMPOSE_FILE_GITHUB" down --remove-orphans 2>/dev/null || true
    docker-compose -f "$COMPOSE_FILE_OFFICIAL" down --remove-orphans 2>/dev/null || true
}

# Main execution
main() {
    print_header
    
    # Set up error handling
    trap cleanup_on_error ERR
    
    # Check prerequisites
    check_prerequisites
    
    # Test official images first
    if test_official_images; then
        print_info "Using official Docker Hub images"
        setup_environment
        deploy_services "$COMPOSE_FILE_OFFICIAL"
    else
        # Prompt for GitHub fallback
        if prompt_github_fallback; then
            print_info "Using GitHub Container Registry images"
            setup_environment
            pull_github_images
            deploy_services "$COMPOSE_FILE_GITHUB"
        else
            print_error "Deployment cancelled by user"
            exit 1
        fi
    fi
    
    # Display access information
    display_access_info
}

# Run main function
main "$@"

# ROBO-RESEARCHER-2000 n8n Configuration Guide

## 🚀 Quick Setup Instructions

### 1. Access n8n Interface
- **URL**: http://localhost:5678
- **Username**: admin
- **Password**: robo-researcher-2000

### 2. Import Workflows

#### Import Main Workflow
1. Go to **Workflows** → **Import from file**
2. Select `workflows/main-workflow-complete.json`
3. Click **Import**

#### Import Test Workflow
1. Go to **Workflows** → **Import from file**
2. Select `workflows/test-workflow-simple.json`
3. Click **Import**

### 3. Configure Credentials

#### OpenRouter API Credential
1. Go to **Credentials** → **Add Credential**
2. Select **HTTP Request Auth**
3. Name: `OpenRouter API`
4. Authentication: `Header Auth`
5. Header Name: `Authorization`
6. Header Value: `Bearer YOUR_OPENROUTER_API_KEY_HERE`
7. **Save**

#### SMTP Email Credential
1. Go to **Credentials** → **Add Credential**
2. Select **SMTP**
3. Name: `SMTP Email`
4. Host: `mail.stargety.com`
5. Port: `465`
6. Security: `SSL/TLS`
7. Username: `<EMAIL>`
8. Password: `YOUR_SMTP_PASSWORD_HERE`
9. **Save**

#### MinIO S3 Credential
1. Go to **Credentials** → **Add Credential**
2. Select **AWS S3**
3. Name: `MinIO Storage`
4. Access Key ID: `minioadmin`
5. Secret Access Key: `minioadmin`
6. Region: `us-east-1`
7. Custom S3 Endpoint: `http://robo-researcher-minio:9000`
8. Force Path Style: `Yes`
9. **Save**

### 4. Configure Workflow Nodes

#### Update OpenRouter Nodes
1. Open the main workflow
2. Find all **HTTP Request** nodes that call OpenRouter
3. Set **Authentication** to `OpenRouter API` credential
4. Update **URL** to: `https://openrouter.ai/api/v1/chat/completions`

#### Update Email Nodes
1. Find all **Send Email** nodes
2. Set **Credential** to `SMTP Email`
3. Update **From Email** to: `<EMAIL>`

#### Update MinIO Nodes
1. Find all **AWS S3** nodes
2. Set **Credential** to `MinIO Storage`
3. Update **Bucket Name** to: `robo-researcher-data`

### 5. Test Workflows

#### Test Simple Workflow
1. Open `test-workflow-simple.json`
2. Click **Execute Workflow**
3. Verify it completes successfully

#### Test Main Workflow
1. Open `main-workflow-complete.json`
2. Use the webhook URL: `http://localhost:5678/webhook/robo-researcher`
3. Send a test POST request with sample data

### 6. Access URLs

- **Client Application**: http://localhost:8080
- **n8n Interface**: http://localhost:5678
- **MinIO Console**: http://localhost:9003
- **Wiki.js**: http://localhost:3002

### 7. Required API Keys

Update these in your `.env` file:

```bash
# OpenRouter API Key (REQUIRED)
OPENROUTER_API_KEY=sk-or-v1-your-key-here

# SMTP Password (REQUIRED)
SMTP_PASSWORD=your-smtp-password-here
```

### 8. Troubleshooting

#### Workflow Execution Errors
- Check **Executions** tab for error details
- Verify all credentials are properly configured
- Ensure API keys are valid and have sufficient quota

#### Connection Issues
- Verify all containers are healthy: `docker ps`
- Check container logs: `docker logs robo-researcher-n8n`
- Restart services if needed: `docker compose restart`

#### MinIO Bucket Creation
If the bucket doesn't exist:
1. Access MinIO Console: http://localhost:9003
2. Login: minioadmin / minioadmin
3. Create bucket: `robo-researcher-data`
4. Set public read policy if needed

## 🎯 Next Steps

1. Configure all credentials with real API keys
2. Test the simple workflow first
3. Import and test the main workflow
4. Use the client application to submit a test transcription
5. Monitor execution in n8n interface

## 📞 Support

If you encounter issues:
- Check the container logs
- Verify network connectivity between services
- Ensure all required ports are accessible
- Review the workflow documentation in `workflows/workflow-documentation.md`

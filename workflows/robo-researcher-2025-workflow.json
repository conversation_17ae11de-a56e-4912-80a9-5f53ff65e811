{"name": "ROBO-RESEARCHER-2000 Complete Workflow v2025", "nodes": [{"parameters": {"httpMethod": "POST", "path": "robo-researcher", "responseMode": "responseNode", "options": {"allowedOrigins": "*"}}, "id": "webhook-trigger", "name": "1. <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.webhook", "typeVersion": 2.1, "position": [240, 300], "webhookId": "robo-researcher"}, {"parameters": {"jsCode": "// Step 2: Validate Input Data\nconst inputData = $input.all()[0].json;\nconst requiredFields = ['projectName', 'email', 'transcription'];\nconst missingFields = requiredFields.filter(field => !inputData[field]);\n\nif (missingFields.length > 0) {\n  return [{\n    json: {\n      success: false,\n      error: `Missing required fields: ${missingFields.join(', ')}`,\n      step: 'validation',\n      timestamp: new Date().toISOString()\n    }\n  }];\n}\n\n// Email validation\nconst emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\nif (!emailRegex.test(inputData.email)) {\n  return [{\n    json: {\n      success: false,\n      error: 'Invalid email format',\n      step: 'validation',\n      timestamp: new Date().toISOString()\n    }\n  }];\n}\n\n// Transcription length validation\nif (inputData.transcription.length < 100) {\n  return [{\n    json: {\n      success: false,\n      error: 'Transcription too short (minimum 100 characters)',\n      step: 'validation',\n      timestamp: new Date().toISOString()\n    }\n  }];\n}\n\n// Generate execution ID\nconst executionId = `exec_${new Date().toISOString().replace(/[:.]/g, '-')}`;\n\nreturn [{\n  json: {\n    success: true,\n    executionId,\n    projectName: inputData.projectName,\n    email: inputData.email,\n    transcription: inputData.transcription,\n    studyType: inputData.studyType || 'user_interview',\n    objectives: inputData.objectives || '',\n    language: inputData.language || 'en',\n    analysisDepth: inputData.analysisDepth || 'standard',\n    enableSentiment: inputData.enableSentiment !== false,\n    enableEntities: inputData.enableEntities !== false,\n    generatePresentation: inputData.generatePresentation !== false,\n    step: 'validation_complete',\n    timestamp: new Date().toISOString()\n  }\n}];"}, "id": "validate-input", "name": "2. Validate Input", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"operation": "upload", "bucketName": "robo-researcher-data", "fileName": "={{ $json.executionId }}/original.txt", "binaryData": false, "fileContent": "={{ $json.transcription }}"}, "id": "upload-to-minio", "name": "3. Upload to MinIO", "type": "n8n-nodes-base.s3", "typeVersion": 1, "position": [680, 300], "credentials": {"s3": {"id": "minio-credentials", "name": "MinIO S3 Compatible"}}}, {"parameters": {"jsCode": "// Step 4: Text Preprocessing\nconst data = $input.all()[0].json;\nlet text = data.transcription;\n\n// Basic text cleaning\ntext = text\n  .replace(/\\r\\n/g, '\\n')  // Normalize line endings\n  .replace(/\\s+/g, ' ')    // Normalize whitespace\n  .trim();\n\n// Remove or anonymize PII (basic patterns)\ntext = text\n  .replace(/\\b\\d{3}-\\d{2}-\\d{4}\\b/g, '[SSN]')  // SSN\n  .replace(/\\b\\d{3}-\\d{3}-\\d{4}\\b/g, '[PHONE]')  // Phone\n  .replace(/\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b/g, '[EMAIL]');  // Email\n\n// Split into segments (by speaker or topic)\nconst segments = text.split(/\\n\\n+/).filter(seg => seg.trim().length > 0);\n\nreturn [{\n  json: {\n    ...data,\n    cleanedText: text,\n    segments: segments,\n    segmentCount: segments.length,\n    wordCount: text.split(/\\s+/).length,\n    step: 'preprocessing_complete',\n    timestamp: new Date().toISOString()\n  }\n}];"}, "id": "text-preprocessing", "name": "4. Text Preprocessing", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"jsCode": "// Step 5: Segmentation\nconst data = $input.all()[0].json;\nconst segments = data.segments;\n\n// Enhanced segmentation by topics/themes\nconst topicSegments = [];\nlet currentTopic = null;\nlet currentSegment = [];\n\n// Keywords for topic detection\nconst topicKeywords = {\n  'usability': ['easy', 'difficult', 'confusing', 'intuitive', 'user-friendly', 'navigation'],\n  'design': ['design', 'layout', 'visual', 'color', 'interface', 'appearance'],\n  'functionality': ['feature', 'function', 'work', 'broken', 'bug', 'error'],\n  'performance': ['slow', 'fast', 'loading', 'speed', 'responsive', 'lag'],\n  'satisfaction': ['like', 'love', 'hate', 'frustrated', 'satisfied', 'happy']\n};\n\nsegments.forEach((segment, index) => {\n  const segmentLower = segment.toLowerCase();\n  \n  // Detect topic based on keywords\n  let detectedTopic = 'general';\n  let maxScore = 0;\n  \n  Object.entries(topicKeywords).forEach(([topic, keywords]) => {\n    const score = keywords.reduce((acc, keyword) => {\n      return acc + (segmentLower.includes(keyword) ? 1 : 0);\n    }, 0);\n    \n    if (score > maxScore) {\n      maxScore = score;\n      detectedTopic = topic;\n    }\n  });\n  \n  topicSegments.push({\n    id: index + 1,\n    text: segment,\n    topic: detectedTopic,\n    wordCount: segment.split(/\\s+/).length,\n    sentiment: null  // Will be filled in later steps\n  });\n});\n\nreturn [{\n  json: {\n    ...data,\n    topicSegments: topicSegments,\n    topicCount: [...new Set(topicSegments.map(s => s.topic))].length,\n    step: 'segmentation_complete',\n    timestamp: new Date().toISOString()\n  }\n}];"}, "id": "segmentation", "name": "5. Segmentation", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"jsCode": "// Step 6: Deductive Coding\nconst data = $input.all()[0].json;\nconst segments = data.topicSegments;\n\n// Predefined coding framework (based on UX research best practices)\nconst codingFramework = {\n  'usability_issues': {\n    'navigation_problems': ['lost', 'confused', 'where is', 'cannot find'],\n    'interaction_difficulties': ['click', 'tap', 'button', 'not working'],\n    'cognitive_load': ['too much', 'overwhelming', 'complex', 'complicated']\n  },\n  'user_emotions': {\n    'frustration': ['frustrated', 'annoying', 'irritating', 'hate'],\n    'satisfaction': ['love', 'like', 'enjoy', 'pleased', 'happy'],\n    'confusion': ['confused', 'unclear', 'dont understand', 'what does']\n  },\n  'feature_feedback': {\n    'missing_features': ['wish', 'would like', 'need', 'missing'],\n    'feature_requests': ['add', 'include', 'want', 'should have'],\n    'feature_praise': ['great feature', 'useful', 'helpful', 'convenient']\n  }\n};\n\n// Apply deductive codes to segments\nconst codedSegments = segments.map(segment => {\n  const codes = [];\n  const text = segment.text.toLowerCase();\n  \n  Object.entries(codingFramework).forEach(([category, subcategories]) => {\n    Object.entries(subcategories).forEach(([code, keywords]) => {\n      const matches = keywords.filter(keyword => text.includes(keyword));\n      if (matches.length > 0) {\n        codes.push({\n          category,\n          code,\n          matches,\n          confidence: matches.length / keywords.length\n        });\n      }\n    });\n  });\n  \n  return {\n    ...segment,\n    deductiveCodes: codes\n  };\n});\n\nreturn [{\n  json: {\n    ...data,\n    codedSegments,\n    step: 'deductive_coding_complete',\n    timestamp: new Date().toISOString()\n  }\n}];"}, "id": "deductive-coding", "name": "6. Deductive Coding", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 300]}, {"parameters": {"model": "gpt-4o-mini", "messages": {"values": [{"content": "=You are an expert UX researcher. Analyze the following interview segments and suggest emergent codes that capture themes not covered by the predefined coding framework.\n\nSegments to analyze:\n{{ $json.codedSegments.map(s => `Segment ${s.id}: ${s.text}`).join('\\n\\n') }}\n\nExisting codes applied:\n{{ $json.codedSegments.flatMap(s => s.deductiveCodes.map(c => c.code)).join(', ') }}\n\nPlease suggest 5-10 emergent codes that capture additional themes, patterns, or insights. For each code, provide:\n1. Code name\n2. Description\n3. Example quotes that support this code\n4. Frequency estimate\n\nRespond in JSON format with an array of emergent codes."}]}, "options": {"temperature": 0.3}}, "id": "open-coding-ai", "name": "7. Open Coding AI", "type": "n8n-nodes-base.openAi", "typeVersion": 1.4, "position": [1560, 300], "credentials": {"openAiApi": {"id": "openrouter-credentials", "name": "OpenRouter API"}}}], "connections": {"1. Webhook Trigger": {"main": [[{"node": "2. Validate Input", "type": "main", "index": 0}]]}, "2. Validate Input": {"main": [[{"node": "3. Upload to MinIO", "type": "main", "index": 0}]]}, "3. Upload to MinIO": {"main": [[{"node": "4. Text Preprocessing", "type": "main", "index": 0}]]}, "4. Text Preprocessing": {"main": [[{"node": "5. Segmentation", "type": "main", "index": 0}]]}, "5. Segmentation": {"main": [[{"node": "6. Deductive Coding", "type": "main", "index": 0}]]}, "6. Deductive Coding": {"main": [[{"node": "7. Open Coding AI", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "", "timezone": "America/New_York"}, "versionId": "2025.1", "meta": {"templateCredsSetupCompleted": false, "instanceId": "robo-researcher-2000"}, "id": "robo-researcher-2025", "tags": [{"id": "robo-researcher", "name": "robo-researcher"}, {"id": "ux-research", "name": "ux-research"}, {"id": "ai-automation", "name": "ai-automation"}]}
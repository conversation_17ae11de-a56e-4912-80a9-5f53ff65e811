# ROBO-RESEARCHER-2000 Workflow Setup Guide

## Prerequisites

Before setting up the workflows, ensure you have:

1. **n8n instance running** on `http://localhost:5678`
2. **MinIO server** running on `http://localhost:9000`
3. **Wiki.js instance** running on `http://localhost:3000`
4. **OpenRouter API key** for AI analysis
5. **SMTP credentials** for email notifications

## Quick Setup

### 1. Start Infrastructure

```bash
# Navigate to infrastructure directory
cd infrastructure

# Start all services
./setup.sh

# Or manually with <PERSON><PERSON> Compose
docker-compose up -d
```

### 2. Access n8n

1. Open `http://localhost:5678` in your browser
2. Complete initial setup if first time
3. Login with your credentials

### 3. Import Test Workflow

1. In n8n, go to **Workflows** → **Import from file**
2. Select `workflows/test-workflow-simple.json`
3. Click **Import**
4. Activate the workflow

### 4. Test Basic Functionality

```bash
# Test the webhook endpoint
curl -X POST http://localhost:5678/webhook/test-robo-researcher \
  -H "Content-Type: application/json" \
  -d '{
    "project_name": "Test Project",
    "email": "<EMAIL>",
    "transcription": "This is a test transcription. The user found the interface easy to use and navigation was clear."
  }'
```

Expected response:
```json
{
  "success": true,
  "project_name": "Test Project",
  "analysis_summary": {
    "word_count": 18,
    "overall_sentiment": "positive"
  },
  "test_mode": true
}
```

## Full Workflow Setup

### 1. Configure Credentials

In n8n, go to **Settings** → **Credentials** and add:

#### OpenRouter API
- **Type**: HTTP Header Auth
- **Name**: `Authorization`
- **Value**: `Bearer YOUR_OPENROUTER_API_KEY`

#### MinIO S3
- **Type**: S3
- **Access Key ID**: `minioadmin`
- **Secret Access Key**: `minioadmin`
- **Region**: `us-east-1`
- **Custom S3 Endpoint**: `http://localhost:9000`
- **Force Path Style**: `true`

#### SMTP Email
- **Type**: SMTP
- **Host**: `smtp.gmail.com`
- **Port**: `587`
- **Security**: `STARTTLS`
- **Username**: `<EMAIL>`
- **Password**: `your-app-password`

#### Wiki.js API
- **Type**: HTTP Header Auth
- **Name**: `Authorization`
- **Value**: `Bearer YOUR_WIKIJS_API_TOKEN`

### 2. Import Main Workflow

1. Import `workflows/main-workflow-complete.json`
2. Assign credentials to appropriate nodes:
   - **Open Coding AI** → OpenRouter API credential
   - **Upload to MinIO** → MinIO S3 credential
   - **Documentation & Email** → SMTP and Wiki.js credentials

### 3. Configure Environment Variables

Create `.env` file in the infrastructure directory:

```bash
# n8n Configuration
N8N_WEBHOOK_URL=http://localhost:5678/webhook/robo-researcher

# MinIO Configuration
MINIO_ENDPOINT=http://localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin

# Wiki.js Configuration
WIKIJS_URL=http://localhost:3000
WIKIJS_API_TOKEN=your_token_here

# OpenRouter Configuration
OPENROUTER_API_KEY=your_key_here

# SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
```

### 4. Test Full Workflow

```bash
# Test with complete data
curl -X POST http://localhost:5678/webhook/robo-researcher \
  -H "Content-Type: application/json" \
  -d '{
    "project_name": "Mobile App UX Research",
    "email": "<EMAIL>",
    "transcription": "Interviewer: How did you find the navigation? User: It was quite confusing at first. I couldn'\''t find the search function easily. Interviewer: What about the overall design? User: The design looks good, but some buttons are too small.",
    "study_type": "user_interview",
    "objectives": "Evaluate mobile app usability",
    "language": "en",
    "api_keys": {
      "openrouter": "your_openrouter_key",
      "smtp_password": "your_smtp_password"
    },
    "options": {
      "enable_sentiment": true,
      "enable_entities": true,
      "generate_presentation": true
    }
  }'
```

## Troubleshooting

### Common Issues

#### 1. Webhook Not Responding
- Check if n8n is running: `docker ps | grep n8n`
- Verify workflow is active in n8n UI
- Check n8n logs: `docker logs robo-researcher-n8n`

#### 2. MinIO Upload Failures
- Verify MinIO is accessible: `curl http://localhost:9000/minio/health/live`
- Check bucket exists: Access MinIO console at `http://localhost:9001`
- Verify credentials in n8n

#### 3. AI API Errors
- Verify OpenRouter API key is valid
- Check API quota and usage limits
- Review error messages in n8n execution logs

#### 4. Email Delivery Issues
- Verify SMTP credentials
- Check if app passwords are enabled (Gmail)
- Test SMTP connection independently

### Debug Mode

Enable debug logging in n8n:

```bash
# In docker-compose.yml
environment:
  - N8N_LOG_LEVEL=debug
```

### Health Checks

Verify all services are healthy:

```bash
# Check n8n
curl http://localhost:5678/healthz

# Check MinIO
curl http://localhost:9000/minio/health/live

# Check Wiki.js
curl http://localhost:3000/healthz
```

## Performance Optimization

### Resource Allocation

For optimal performance, allocate:
- **n8n**: 2 CPU cores, 2GB RAM
- **MinIO**: 1 CPU core, 1GB RAM
- **Wiki.js**: 1 CPU core, 1GB RAM
- **PostgreSQL**: 1 CPU core, 1GB RAM

### Workflow Optimization

1. **Parallel Processing**: Some steps can run in parallel
2. **Caching**: Cache AI responses for similar inputs
3. **Batch Processing**: Process multiple transcriptions together
4. **Resource Limits**: Set timeouts for long-running operations

### Monitoring

Set up monitoring for:
- Workflow execution times
- API response times
- Error rates
- Resource usage

## Security Considerations

### API Keys
- Store API keys securely in n8n credentials
- Rotate keys regularly
- Use environment variables for sensitive data

### Network Security
- Use HTTPS in production
- Implement proper firewall rules
- Consider VPN for remote access

### Data Privacy
- Anonymize personal data in transcriptions
- Implement data retention policies
- Secure backup procedures

## Scaling

### Horizontal Scaling
- Multiple n8n instances with load balancer
- Distributed MinIO cluster
- Database read replicas

### Vertical Scaling
- Increase container resources
- Optimize database queries
- Use faster storage (SSD)

## Backup and Recovery

### Automated Backups
```bash
# Backup n8n workflows
docker exec robo-researcher-n8n n8n export:workflow --all --output=/backups/

# Backup MinIO data
mc mirror robo-researcher/robo-researcher-data /backups/minio/

# Backup PostgreSQL
docker exec robo-researcher-postgres pg_dump -U n8n n8n > /backups/postgres.sql
```

### Recovery Procedures
1. Restore service containers
2. Import workflow backups
3. Restore MinIO data
4. Restore database

This setup guide provides comprehensive instructions for deploying and configuring the ROBO-RESEARCHER-2000 workflow system.

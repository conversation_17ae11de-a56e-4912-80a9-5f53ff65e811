# ROBO-RESEARCHER-2000 Integration Guide

## Overview

This guide provides comprehensive instructions for integrating and testing the complete ROBO-RESEARCHER-2000 system. Follow these steps to ensure all components work together seamlessly.

## Prerequisites

Before starting integration, ensure you have:

- **Docker & Docker Compose** installed
- **Python 3.8+** with pip
- **Node.js 16+** (for client development)
- **Git** for version control
- **OpenRouter API key** for AI functionality
- **SMTP credentials** for email notifications

## Integration Steps

### 1. Infrastructure Setup

```bash
# Clone the repository
git clone <repository-url>
cd robo-researcher-2000

# Start infrastructure services
cd infrastructure
chmod +x setup.sh
./setup.sh

# Verify services are running
docker ps
```

Expected containers:
- `robo-researcher-n8n` (port 5678)
- `robo-researcher-minio` (port 9000)
- `robo-researcher-postgres` (port 5432)
- `robo-researcher-wikijs` (port 3000)

### 2. Environment Configuration

Create `.env` file in the root directory:

```bash
# API Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password

# Service URLs
N8N_URL=http://localhost:5678
MINIO_URL=http://localhost:9000
WIKIJS_URL=http://localhost:3000

# MinIO Configuration
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
```

### 3. n8n Workflow Setup

1. **Access n8n**: Open `http://localhost:5678`
2. **Complete setup**: Create admin account if first time
3. **Import workflows**:
   - Go to **Workflows** → **Import from file**
   - Import `workflows/test-workflow-simple.json`
   - Import `workflows/main-workflow-complete.json`
4. **Configure credentials**:
   - **OpenRouter API**: Add HTTP Header Auth credential
   - **MinIO S3**: Add S3 credential with local settings
   - **SMTP**: Add email credential for notifications

### 4. Client Application Setup

```bash
# Navigate to client directory
cd client

# Serve the application (using Python's built-in server)
python -m http.server 8080

# Or use Node.js serve
npx serve . -p 8080
```

Access the client at `http://localhost:8080`

### 5. Python Dependencies

```bash
# Install Python dependencies
pip install -r requirements.txt

# Or install individually
pip install requests nltk spacy pandas numpy matplotlib seaborn scikit-learn
```

### 6. Validation and Testing

Run the comprehensive test suite:

```bash
# Make test scripts executable
chmod +x tests/*.py

# Run deployment validation
python tests/deployment_validator.py

# Run integration tests
python tests/integration_test.py

# Run complete test suite
python tests/run_tests.py --mode all
```

## Component Integration Details

### n8n Workflow Integration

The system uses two main workflows:

#### Test Workflow (`test-workflow-simple.json`)
- **Purpose**: Quick validation and testing
- **Endpoint**: `/webhook/test-robo-researcher`
- **Processing**: Basic validation and sentiment analysis
- **Response Time**: < 5 seconds

#### Main Workflow (`main-workflow-complete.json`)
- **Purpose**: Complete 17-step analysis
- **Endpoint**: `/webhook/robo-researcher`
- **Processing**: Full UX research analysis pipeline
- **Response Time**: 15-20 minutes

### Data Flow Integration

```
Client Web App → n8n Webhook → Processing Pipeline → Results Storage → Email Notification
     ↓              ↓              ↓                    ↓               ↓
  Form Data    Validation    Text Processing      MinIO Storage    SMTP Email
                              ↓                        ↓
                         AI Analysis              Wiki.js Docs
                              ↓
                        Presentation Gen
```

### Storage Integration

#### MinIO Object Storage
- **Bucket**: `robo-researcher-data`
- **Structure**:
  ```
  /transcripts/{execution_id}/original.txt
  /results/{execution_id}/analysis.json
  /presentations/{execution_id}/slides.pptx
  /visualizations/{execution_id}/affinity-map.svg
  ```

#### Wiki.js Documentation
- **Base URL**: `http://localhost:3000`
- **API Endpoint**: `/graphql`
- **Structure**: `/research/{project-name}`

### API Integration Points

#### OpenRouter AI API
- **Endpoint**: `https://openrouter.ai/api/v1/chat/completions`
- **Model**: `anthropic/claude-3-sonnet`
- **Usage**: Open coding, pattern detection, insight generation

#### SMTP Email Integration
- **Purpose**: Result notifications and delivery
- **Attachments**: Presentation files, analysis summaries
- **Templates**: HTML email with embedded results

## Testing Procedures

### 1. Unit Testing

Test individual components:

```bash
# Test text preprocessing
python -c "from scripts.text_preprocessing import *; print('✅ Text preprocessing OK')"

# Test coding engine
python -c "from scripts.coding_engine import *; print('✅ Coding engine OK')"

# Test quantitative analyzer
python -c "from scripts.quantitative_analyzer import *; print('✅ Quantitative analyzer OK')"
```

### 2. Integration Testing

```bash
# Run integration test suite
python tests/integration_test.py

# Test specific components
python tests/run_tests.py --mode integration
```

### 3. End-to-End Testing

#### Using Client Application
1. Open `http://localhost:8080`
2. Fill out the form with test data
3. Upload a sample transcription
4. Submit and monitor progress
5. Verify results delivery

#### Using API Directly
```bash
curl -X POST http://localhost:5678/webhook/test-robo-researcher \
  -H "Content-Type: application/json" \
  -d '{
    "project_name": "Integration Test",
    "email": "<EMAIL>",
    "transcription": "User: The navigation was confusing. Interviewer: What would you improve? User: Make the menu clearer and add search."
  }'
```

### 4. Performance Testing

```bash
# Run performance tests
python tests/run_tests.py --mode performance

# Monitor resource usage
docker stats

# Check response times
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:5678/healthz
```

## Troubleshooting Integration Issues

### Common Problems

#### 1. Services Not Starting
```bash
# Check Docker status
docker ps -a

# View service logs
docker logs robo-researcher-n8n
docker logs robo-researcher-minio
docker logs robo-researcher-postgres
```

#### 2. Webhook Not Responding
- Verify n8n is running: `curl http://localhost:5678/healthz`
- Check workflow is active in n8n UI
- Verify webhook URL in client configuration

#### 3. AI API Failures
- Verify OpenRouter API key is valid
- Check API quota and rate limits
- Review error messages in n8n execution logs

#### 4. File Upload Issues
- Verify MinIO is accessible: `curl http://localhost:9000/minio/health/live`
- Check bucket permissions and credentials
- Verify file size limits

#### 5. Email Delivery Problems
- Test SMTP credentials independently
- Check firewall and network settings
- Verify email templates and formatting

### Debug Mode

Enable debug logging:

```bash
# Set environment variables
export N8N_LOG_LEVEL=debug
export PYTHONPATH=$PWD/scripts

# Restart services with debug logging
docker-compose down
docker-compose up -d
```

### Health Checks

Monitor system health:

```bash
# Check all services
python tests/deployment_validator.py

# Individual service checks
curl http://localhost:5678/healthz    # n8n
curl http://localhost:9000/minio/health/live  # MinIO
curl http://localhost:3000/healthz   # Wiki.js
```

## Production Deployment Considerations

### Security
- Change default passwords and API keys
- Enable HTTPS for all services
- Configure proper firewall rules
- Implement API rate limiting

### Scalability
- Use external databases for production
- Implement load balancing for n8n
- Configure MinIO clustering
- Set up monitoring and alerting

### Backup and Recovery
- Regular database backups
- MinIO data replication
- Workflow export procedures
- Disaster recovery testing

### Monitoring
- Service health monitoring
- Performance metrics collection
- Error rate tracking
- User activity analytics

## Support and Maintenance

### Regular Maintenance Tasks
- Update Docker images monthly
- Review and rotate API keys quarterly
- Clean up old analysis data
- Monitor storage usage

### Monitoring Dashboards
- Service uptime and health
- Workflow execution metrics
- API usage and quotas
- Storage utilization

### Backup Procedures
```bash
# Backup workflows
docker exec robo-researcher-n8n n8n export:workflow --all

# Backup MinIO data
mc mirror robo-researcher/robo-researcher-data /backups/

# Backup PostgreSQL
docker exec robo-researcher-postgres pg_dump -U n8n n8n > backup.sql
```

This integration guide ensures all components of ROBO-RESEARCHER-2000 work together seamlessly to provide automated UX research analysis capabilities.

# ROBO-RESEARCHER-2000 Docker Compose - GitHub Images
# Alternative deployment using GitHub Container Registry images
# Use this when official Docker Hub images are not available

version: '3.8'

services:
  # n8n Workflow Engine
  n8n:
    image: ghcr.io/robo-researcher-2000/n8n:latest
    container_name: robo-n8n
    restart: unless-stopped
    ports:
      - "5678:5678"
    environment:
      - N8N_HOST=${N8N_HOST:-localhost}
      - N8N_PROTOCOL=${N8N_PROTOCOL:-http}
      - N8N_SECURE_COOKIE=${N8N_SECURE_COOKIE:-false}
      - N8N_BASIC_AUTH_ACTIVE=${N8N_BASIC_AUTH_ACTIVE:-true}
      - N8N_BASIC_AUTH_USER=${N8N_BASIC_AUTH_USER:-admin}
      - N8N_BASIC_AUTH_PASSWORD=${N8N_BASIC_AUTH_PASSWORD:-robo-researcher-2000}
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_DATABASE=${POSTGRES_DB:-n8n}
      - DB_POSTGRESDB_USER=${POSTGRES_USER:-n8n_user}
      - DB_POSTGRESDB_PASSWORD=${POSTGRES_PASSWORD:-n8n_password}
      - N8N_LOG_LEVEL=${N8N_LOG_LEVEL:-info}
      - GENERIC_TIMEZONE=${GENERIC_TIMEZONE:-UTC}
      - N8N_METRICS=true
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./workflows:/home/<USER>/.n8n/workflows:ro
      - ./custom-nodes:/home/<USER>/.n8n/custom:ro
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5678/healthz || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - robo-network

  # PostgreSQL Database
  postgres:
    image: ghcr.io/robo-researcher-2000/postgres:15
    container_name: robo-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-n8n}
      - POSTGRES_USER=${POSTGRES_USER:-n8n_user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-n8n_password}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-n8n_user} -d ${POSTGRES_DB:-n8n}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - robo-network

  # MinIO Object Storage
  minio:
    image: ghcr.io/robo-researcher-2000/minio:latest
    container_name: robo-minio
    restart: unless-stopped
    ports:
      - "9002:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=${MINIO_ROOT_USER:-minioadmin}
      - MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD:-minioadmin}
      - MINIO_BROWSER_REDIRECT_URL=http://localhost:9001
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
      start_period: 30s
    networks:
      - robo-network

  # Wiki.js Documentation
  wikijs:
    image: ghcr.io/robo-researcher-2000/wikijs:2
    container_name: robo-wikijs
    restart: unless-stopped
    ports:
      - "3002:3000"
    environment:
      - DB_TYPE=postgres
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=${POSTGRES_DB:-n8n}
      - DB_USER=${POSTGRES_USER:-n8n_user}
      - DB_PASS=${POSTGRES_PASSWORD:-n8n_password}
      - WIKI_ADMIN_EMAIL=${WIKI_ADMIN_EMAIL:-<EMAIL>}
    volumes:
      - wikijs_data:/wiki
      - ./wiki-config:/wiki/config:ro
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - robo-network

  # Redis Cache
  redis:
    image: ghcr.io/robo-researcher-2000/redis:7-alpine
    container_name: robo-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password}
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - robo-network

  # Client Web Interface
  client:
    image: ghcr.io/robo-researcher-2000/client:latest
    container_name: robo-client
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      - N8N_WEBHOOK_URL=http://localhost:5678/webhook/robo-researcher
      - WIKIJS_URL=http://localhost:3002
      - MINIO_URL=http://localhost:9002
    volumes:
      - ./client-config:/usr/share/nginx/html/config:ro
    depends_on:
      - n8n
      - wikijs
      - minio
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:80 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - robo-network

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: ghcr.io/robo-researcher-2000/nginx:alpine
    container_name: robo-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - client
      - n8n
      - wikijs
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:80/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - robo-network
    profiles:
      - production

volumes:
  n8n_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./volumes/n8n_data
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./volumes/postgres_data
  minio_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./volumes/minio_data
  wikijs_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./volumes/wikijs_data
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./volumes/redis_data
  nginx_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./volumes/nginx_logs

networks:
  robo-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Health check configuration
x-healthcheck-defaults: &healthcheck-defaults
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 30s

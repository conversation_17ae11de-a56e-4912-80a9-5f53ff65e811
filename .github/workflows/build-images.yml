name: Build and Push Docker Images

on:
  push:
    branches: [ main, develop ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      force_rebuild:
        description: 'Force rebuild all images'
        required: false
        default: 'false'
        type: boolean

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: robo-researcher-2000

jobs:
  build-matrix:
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
    steps:
      - name: Set up build matrix
        id: set-matrix
        run: |
          echo 'matrix={
            "include": [
              {
                "service": "n8n",
                "base_image": "n8nio/n8n:latest",
                "dockerfile": "dockerfiles/Dockerfile.n8n",
                "context": "."
              },
              {
                "service": "postgres",
                "base_image": "postgres:15",
                "dockerfile": "dockerfiles/Dockerfile.postgres",
                "context": "."
              },
              {
                "service": "minio",
                "base_image": "minio/minio:latest",
                "dockerfile": "dockerfiles/Dockerfile.minio",
                "context": "."
              },
              {
                "service": "wikijs",
                "base_image": "requarks/wiki:2",
                "dockerfile": "dockerfiles/Dockerfile.wikijs",
                "context": "."
              },
              {
                "service": "redis",
                "base_image": "redis:7-alpine",
                "dockerfile": "dockerfiles/Dockerfile.redis",
                "context": "."
              },
              {
                "service": "nginx",
                "base_image": "nginx:alpine",
                "dockerfile": "dockerfiles/Dockerfile.nginx",
                "context": "."
              },
              {
                "service": "client",
                "base_image": "nginx:alpine",
                "dockerfile": "dockerfiles/Dockerfile.client",
                "context": "./client"
              }
            ]
          }' >> $GITHUB_OUTPUT

  build-and-push:
    needs: build-matrix
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix: ${{fromJson(needs.build-matrix.outputs.matrix)}}
    
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Create Dockerfile if not exists
        run: |
          mkdir -p dockerfiles
          if [ ! -f "${{ matrix.dockerfile }}" ]; then
            cat > "${{ matrix.dockerfile }}" << EOF
          FROM ${{ matrix.base_image }}
          
          # Add ROBO-RESEARCHER-2000 labels
          LABEL org.opencontainers.image.source=https://github.com/${{ github.repository }}
          LABEL org.opencontainers.image.description="ROBO-RESEARCHER-2000 ${{ matrix.service }} service"
          LABEL org.opencontainers.image.licenses=MIT
          LABEL org.opencontainers.image.version=${{ github.ref_name }}
          LABEL org.opencontainers.image.revision=${{ github.sha }}
          LABEL org.opencontainers.image.created=$(date -u +'%Y-%m-%dT%H:%M:%SZ')
          
          # Service-specific customizations
          $(if [ "${{ matrix.service }}" = "n8n" ]; then
            echo "# Install additional n8n nodes"
            echo "USER root"
            echo "RUN npm install -g n8n-nodes-base@latest"
            echo "USER node"
          elif [ "${{ matrix.service }}" = "client" ]; then
            echo "# Copy client files"
            echo "COPY . /usr/share/nginx/html/"
            echo "COPY nginx.conf /etc/nginx/nginx.conf"
          elif [ "${{ matrix.service }}" = "postgres" ]; then
            echo "# Add initialization scripts"
            echo "COPY init-scripts/*.sql /docker-entrypoint-initdb.d/"
          elif [ "${{ matrix.service }}" = "wikijs" ]; then
            echo "# Add Wiki.js customizations"
            echo "ENV WIKI_ADMIN_EMAIL=<EMAIL>"
          fi)
          
          # Health check
          $(if [ "${{ matrix.service }}" = "n8n" ]; then
            echo 'HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \'
            echo '  CMD curl -f http://localhost:5678/healthz || exit 1'
          elif [ "${{ matrix.service }}" = "minio" ]; then
            echo 'HEALTHCHECK --interval=30s --timeout=20s --start-period=30s --retries=3 \'
            echo '  CMD curl -f http://localhost:9000/minio/health/live || exit 1'
          elif [ "${{ matrix.service }}" = "postgres" ]; then
            echo 'HEALTHCHECK --interval=10s --timeout=5s --start-period=30s --retries=5 \'
            echo '  CMD pg_isready -U $$POSTGRES_USER -d $$POSTGRES_DB'
          elif [ "${{ matrix.service }}" = "redis" ]; then
            echo 'HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \'
            echo '  CMD redis-cli ping || exit 1'
          elif [ "${{ matrix.service }}" = "wikijs" ]; then
            echo 'HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \'
            echo '  CMD curl -f http://localhost:3000 || exit 1'
          elif [ "${{ matrix.service }}" = "client" ] || [ "${{ matrix.service }}" = "nginx" ]; then
            echo 'HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \'
            echo '  CMD curl -f http://localhost:80 || exit 1'
          fi)
          EOF
          fi

      - name: Create additional files for client
        if: matrix.service == 'client'
        run: |
          mkdir -p client
          if [ ! -f client/nginx.conf ]; then
            cat > client/nginx.conf << 'EOF'
          events {
              worker_connections 1024;
          }
          
          http {
              include       /etc/nginx/mime.types;
              default_type  application/octet-stream;
              
              sendfile        on;
              keepalive_timeout  65;
              
              server {
                  listen       80;
                  server_name  localhost;
                  
                  location / {
                      root   /usr/share/nginx/html;
                      index  index.html index.htm;
                      try_files $uri $uri/ /index.html;
                  }
                  
                  location /health {
                      access_log off;
                      return 200 "healthy\n";
                      add_header Content-Type text/plain;
                  }
                  
                  error_page   500 502 503 504  /50x.html;
                  location = /50x.html {
                      root   /usr/share/nginx/html;
                  }
              }
          }
          EOF
          fi

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.service }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=semver,pattern={{major}}
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ${{ matrix.context }}
          file: ${{ matrix.dockerfile }}
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          platforms: linux/amd64,linux/arm64
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Test image
        run: |
          # Pull the built image
          docker pull ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.service }}:latest
          
          # Basic smoke test
          if [ "${{ matrix.service }}" = "client" ] || [ "${{ matrix.service }}" = "nginx" ]; then
            docker run --rm -d --name test-${{ matrix.service }} -p 8080:80 \
              ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.service }}:latest
            sleep 10
            curl -f http://localhost:8080/health || curl -f http://localhost:8080/
            docker stop test-${{ matrix.service }}
          else
            echo "Skipping smoke test for ${{ matrix.service }} (requires additional setup)"
          fi

  create-release:
    needs: [build-matrix, build-and-push]
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')
    
    permissions:
      contents: write
      packages: read

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Create Release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: ROBO-RESEARCHER-2000 ${{ github.ref }}
          body: |
            ## ROBO-RESEARCHER-2000 Release ${{ github.ref }}
            
            ### Docker Images Available
            
            All images are available in GitHub Container Registry:
            
            - `ghcr.io/robo-researcher-2000/n8n:${{ github.ref_name }}`
            - `ghcr.io/robo-researcher-2000/postgres:${{ github.ref_name }}`
            - `ghcr.io/robo-researcher-2000/minio:${{ github.ref_name }}`
            - `ghcr.io/robo-researcher-2000/wikijs:${{ github.ref_name }}`
            - `ghcr.io/robo-researcher-2000/redis:${{ github.ref_name }}`
            - `ghcr.io/robo-researcher-2000/nginx:${{ github.ref_name }}`
            - `ghcr.io/robo-researcher-2000/client:${{ github.ref_name }}`
            
            ### Deployment
            
            Use the GitHub images deployment script:
            ```bash
            ./deploy-with-github-images.sh
            ```
            
            Or use the GitHub compose file directly:
            ```bash
            docker-compose -f docker-compose.github.yml up -d
            ```
            
            ### Changes
            
            See the commit history for detailed changes in this release.
          draft: false
          prerelease: false

  update-documentation:
    needs: [build-matrix, build-and-push]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Update README with latest image tags
        run: |
          # Update README.md with latest image information
          sed -i 's|ghcr.io/robo-researcher-2000/\([^:]*\):[^`]*|ghcr.io/robo-researcher-2000/\1:latest|g' README.md
          sed -i 's|ghcr.io/robo-researcher-2000/\([^:]*\):[^)]*|ghcr.io/robo-researcher-2000/\1:latest|g' docs/*.md

      - name: Commit documentation updates
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add README.md docs/
          git diff --staged --quiet || git commit -m "docs: update image tags to latest [skip ci]"
          git push
